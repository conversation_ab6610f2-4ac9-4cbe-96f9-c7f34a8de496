interface BasicOption {
  label: string;
  value: string;
}

type SelectOption = BasicOption;

type TabOption = BasicOption;

interface BasicUserInfo {
  /**
   * 头像
   */
  avatar: string;
  /**
   * 创建时间
   */
  createDt: Date;
  /**
   * 邮箱
   */
  email: string;
  /**
   * 首页地址
   */
  homePath: string;
  /**
   * uid，供前端数据绑定
   */
  id: string;
  /**
   * 手机号
   */
  mobile: string;
  /**
   * 用户昵称
   */
  nickname: string;
  /**
   * 用户角色
   */
  roles?: string[];
  /**
   * 生效状态，1生效，0失效
   */
  status: boolean;
  /**
   * 租户id
   */
  tenantId: string;
  /**
   * 用户id
   */
  userId: string;
  /**
   * 用户名
   */
  username: string;
}

type ClassType = Array<object | string> | object | string;

export type { BasicOption, BasicUserInfo, ClassType, SelectOption, TabOption };
