{"name": "@vben/locales", "version": "5.5.4", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/locales"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@intlify/core-base": "catalog:", "@vben-core/composables": "workspace:*", "vue": "catalog:", "vue-i18n": "catalog:"}}