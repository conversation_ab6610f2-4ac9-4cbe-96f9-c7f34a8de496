import type { RouteMeta as IRouteM<PERSON> } from '@vben-core/typings';

import 'vue-router';

declare module 'vue-router' {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface RouteMeta extends IRouteMeta {}
}

export interface VbenAdminProAppConfigRaw {
  VITE_GLOB_API_URL: string;
  VITE_BASE: string;
  VITE_SESSION_KEY: string;
  VITE_OAUTH_BASE_URL: string;
  VITE_OAUTH_CLIENT_ID: string;
  VITE_OAUTH_CLIENT_SECRET: string;
}

export interface ApplicationConfig {
  base: string;
  apiURL: string;
  sessionKey: string;
  oauthServerUrl: string;
  clientId: string;
  clientSecret: string;
}

declare global {
  interface Window {
    _VBEN_ADMIN_PRO_APP_CONF_: VbenAdminProAppConfigRaw;
  }
}
